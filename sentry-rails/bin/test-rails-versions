#!/usr/bin/env ruby
# frozen_string_literal: true

# Standalone CLI script to test sentry-rails against multiple Rails versions
# Usage:
#   ./bin/test --version 5.0
#   ./bin/test --all
#   ./bin/test --help

require 'optparse'
require 'fileutils'

class RailsVersionTester
  # Supported Rails versions based on CI configuration
  SUPPORTED_VERSIONS = %w[5.0 5.1 5.2 6.0 6.1 7.0 7.1 8.0].freeze

  def initialize
    @options = {}
    @failed_versions = []
  end

  def run(args)
    parse_options(args)

    case
    when @options[:help]
      show_help
    when @options[:list]
      list_versions
    when @options[:all]
      test_all_versions
    when @options[:version]
      test_single_version(@options[:version])
    else
      puts "Error: No action specified. Use --help for usage information."
      exit(1)
    end
  end

  private

  def parse_options(args)
    OptionParser.new do |opts|
      opts.banner = "Usage: #{$0} [options]"

      opts.on("-v", "--version VERSION", "Test specific Rails version") do |version|
        unless SUPPORTED_VERSIONS.include?(version)
          puts "Error: Unsupported Rails version '#{version}'"
          puts "Supported versions: #{SUPPORTED_VERSIONS.join(', ')}"
          exit(1)
        end
        @options[:version] = version
      end

      opts.on("-a", "--all", "Test all supported Rails versions") do
        @options[:all] = true
      end

      opts.on("-l", "--list", "List supported Rails versions") do
        @options[:list] = true
      end

      opts.on("-h", "--help", "Show this help message") do
        @options[:help] = true
      end
    end.parse!(args)
  end

  def show_help
    puts <<~HELP
      Rails Version Tester for sentry-rails

      This script tests sentry-rails against different Rails versions by:
      1. Setting the RAILS_VERSION environment variable
      2. Managing bundle dependencies automatically
      3. Running the test suite in isolated processes
      4. Providing proper exit codes for CI/CD integration

      Usage:
        #{$0} --version 6.1        # Test specific Rails version
        #{$0} --all                # Test all supported versions
        #{$0} --list               # List supported versions
        #{$0} --help               # Show this help

      Supported Rails versions: #{SUPPORTED_VERSIONS.join(', ')}

      Examples:
        #{$0} -v 7.1              # Test Rails 7.1
        #{$0} -a                  # Test all versions
    HELP
  end

  def list_versions
    puts "Supported Rails versions:"
    SUPPORTED_VERSIONS.each do |version|
      puts "  - #{version}"
    end
  end

  def test_all_versions
    puts "Testing sentry-rails against all supported Rails versions: #{SUPPORTED_VERSIONS.join(', ')}"
    puts

    SUPPORTED_VERSIONS.each do |version|
      puts "=" * 60
      puts "Testing Rails #{version}"
      puts "=" * 60

      exit_code = test_rails_version(version)

      if exit_code == 0
        puts "✓ Rails #{version} - PASSED"
      else
        puts "✗ Rails #{version} - FAILED (exit code: #{exit_code})"
        @failed_versions << version
      end
      puts
    end

    print_summary
  end

  def test_single_version(version)
    puts "Testing sentry-rails against Rails #{version}..."
    exit_code = test_rails_version(version)
    exit(exit_code) unless exit_code == 0
  end

  def test_rails_version(version)
    puts "Setting up environment for Rails #{version}..."

    # Set up environment variables
    env = {
      "RAILS_VERSION" => version,
      "BUNDLE_GEMFILE" => File.expand_path("Gemfile", Dir.pwd)
    }

    # Check if bundle update is needed
    if bundle_update_needed?(env)
      puts "Dependencies need to be updated for Rails #{version}..."
      unless update_bundle(env)
        puts "✗ Failed to update bundle for Rails #{version}"
        return 1
      end
    end

    # Run the tests in a separate process
    puts "Running test suite..."
    run_tests(env)
  end

  def bundle_update_needed?(env)
    # Check if Gemfile.lock exists
    gemfile_path = env["BUNDLE_GEMFILE"] || "Gemfile"
    lockfile_path = "#{gemfile_path}.lock"

    return true unless File.exist?(lockfile_path)

    # Check if Gemfile is newer than Gemfile.lock
    return true if File.mtime(gemfile_path) > File.mtime(lockfile_path)

    # For Rails version changes, check if lockfile has incompatible Rails version
    if env["RAILS_VERSION"] && lockfile_has_incompatible_rails_version?(lockfile_path, env["RAILS_VERSION"])
      return true
    end

    # Check if bundle check passes
    system(env, "bundle check > /dev/null 2>&1") == false
  end

  def lockfile_has_incompatible_rails_version?(lockfile_path, target_rails_version)
    return false unless File.exist?(lockfile_path)

    lockfile_content = File.read(lockfile_path)

    # Extract Rails version from lockfile
    if lockfile_content =~ /^\s*rails \(([^)]+)\)/
      locked_rails_version = $1
      target_major_minor = target_rails_version.split('.')[0..1].join('.')
      locked_major_minor = locked_rails_version.split('.')[0..1].join('.')

      # If major.minor versions don't match, we need to update
      return target_major_minor != locked_major_minor
    end

    # If we can't determine the Rails version, assume update is needed
    true
  end

  def update_bundle(env)
    puts "Updating bundle for Rails #{env['RAILS_VERSION']}..."

    # Try bundle update first
    if system(env, "bundle update --quiet")
      puts "Bundle updated successfully"
      return true
    end

    puts "Bundle update failed, trying clean install..."

    # Remove lockfile and try fresh install
    lockfile = "#{env['BUNDLE_GEMFILE']}.lock"
    File.delete(lockfile) if File.exist?(lockfile)

    if system(env, "bundle install --quiet")
      puts "Bundle installed successfully"
      return true
    end

    puts "Bundle install failed"
    false
  end

  def run_tests(env)
    # Run the tests in a separate process with proper signal handling
    pid = spawn(env, "bundle exec rake",
               out: $stdout,
               err: $stderr,
               pgroup: true)

    begin
      _, status = Process.wait2(pid)
      status.exitstatus
    rescue Interrupt
      puts "\nInterrupted! Terminating test process..."
      terminate_process_group(pid)
      130 # Standard exit code for SIGINT
    end
  end

  def terminate_process_group(pid)
    begin
      Process.kill("TERM", -pid) # Kill the process group
      sleep(2)
      Process.kill("KILL", -pid) if process_running?(pid)
    rescue Errno::ESRCH
      # Process already terminated
    end
  end

  def process_running?(pid)
    Process.getpgid(pid)
    true
  rescue Errno::ESRCH
    false
  end

  def print_summary
    puts "=" * 60
    puts "SUMMARY"
    puts "=" * 60

    if @failed_versions.empty?
      puts "✓ All Rails versions passed!"
      exit(0)
    else
      puts "✗ Failed versions: #{@failed_versions.join(', ')}"
      puts
      puts "Some Rails versions failed. See output above for details."
      exit(1)
    end
  end
end

# Run the script if called directly
if __FILE__ == $0
  tester = RailsVersionTester.new
  tester.run(ARGV)
end
