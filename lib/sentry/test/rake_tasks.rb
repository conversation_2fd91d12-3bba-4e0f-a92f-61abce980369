# frozen_string_literal: true

require "rake/clean"
require "rspec/core/rake_task"

module Sentry
  module Test
    module RakeTasks
      extend Rake::DSL

      def self.define_spec_tasks(options = {})
        opts = {
          isolated_specs_pattern: "spec/isolated/**/*_spec.rb",
          spec_pattern: nil,
          spec_exclude_pattern: nil,
          spec_rspec_opts: nil,
          isolated_rspec_opts: nil
        }.merge(options)

        RSpec::Core::RakeTask.new(:spec).tap do |task|
          task.pattern = opts[:spec_pattern] if opts[:spec_pattern]
          task.exclude_pattern = opts[:spec_exclude_pattern] if opts[:spec_exclude_pattern]
          task.rspec_opts = opts[:spec_rspec_opts] if opts[:spec_rspec_opts]
        end

        namespace :spec do
          RSpec::Core::RakeTask.new(:isolated).tap do |task|
            task.pattern = opts[:isolated_specs_pattern]
            task.rspec_opts = opts[:isolated_rspec_opts] if opts[:isolated_rspec_opts]
          end
        end
      end

      # Define versioned specs task (sentry-rails specific)
      def self.define_versioned_specs_task(options = {})
        opts = {
          rspec_opts: "--order rand"
        }.merge(options)

        namespace :spec do
          RSpec::Core::RakeTask.new(:versioned).tap do |task|
            ruby_ver_dir = RUBY_VERSION.split(".")[0..1].join(".")
            matching_dir = Dir["spec/versioned/*"].detect { |dir| File.basename(dir) <= ruby_ver_dir }

            unless matching_dir
              puts "No versioned specs found for ruby #{RUBY_VERSION}"
              exit 0
            end

            puts "Running versioned specs from #{matching_dir} for ruby #{RUBY_VERSION}"

            task.rspec_opts = opts[:rspec_opts]
            task.pattern = "#{matching_dir}/**/*_spec.rb"
          end
        end
      end

      # Define Rails version-specific tasks (sentry-rails specific)
      def self.define_rails_version_tasks(options = {})
        # Supported Rails versions based on CI configuration
        supported_rails_versions = %w[5.0 5.1 5.2 6.0 6.1 7.0 7.1 8.0]

        namespace :spec do
          namespace :version do
            # Define individual version tasks for convenience
            supported_rails_versions.each do |version|
              desc "Run specs with Rails #{version}"
              task version do
                exit_code = run_specs_for_rails_version(version)
                exit(exit_code) unless exit_code == 0
              end
            end

            # Define task to run all supported Rails versions
            desc "Run specs for all supported Rails versions"
            task :all do
              puts "Running specs for all supported Rails versions: #{supported_rails_versions.join(', ')}"

              failed_versions = []

              supported_rails_versions.each do |version|
                puts "\n" + "="*60
                puts "Testing Rails #{version}"
                puts "="*60

                exit_code = run_specs_for_rails_version(version)

                if exit_code == 0
                  puts "✓ Rails #{version} - PASSED"
                else
                  puts "✗ Rails #{version} - FAILED (exit code: #{exit_code})"
                  failed_versions << version
                end
              end

              puts "\n" + "="*60
              puts "SUMMARY"
              puts "="*60

              if failed_versions.empty?
                puts "✓ All Rails versions passed!"
              else
                puts "✗ Failed versions: #{failed_versions.join(', ')}"
                exit(1)
              end
            end
          end

          # Define parameterized task that accepts version as argument
          desc "Run specs with specific Rails version (e.g., rake spec:version[6.1])"
          task :version, [:rails_version] do |t, args|
            version = args[:rails_version]
            unless version
              puts "Error: Rails version is required. Usage: rake spec:version[6.1]"
              exit(1)
            end

            unless supported_rails_versions.include?(version)
              puts "Error: Unsupported Rails version '#{version}'"
              puts "Supported versions: #{supported_rails_versions.join(', ')}"
              exit(1)
            end

            exit_code = run_specs_for_rails_version(version)
            exit(exit_code) unless exit_code == 0
          end
        end
      end

      private

      def self.run_specs_for_rails_version(version)
        puts "Running specs with Rails #{version}..."

        # Set up environment
        env = {
          "RAILS_VERSION" => version,
          "BUNDLE_GEMFILE" => File.expand_path("Gemfile", Dir.pwd)
        }

        # Check if bundle update is needed
        if bundle_update_needed?(env)
          puts "Dependencies need to be updated for Rails #{version}..."
          unless update_bundle(env)
            puts "✗ Failed to update bundle for Rails #{version}"
            return 1
          end
        end

        # Run the tests in a separate process
        puts "Running test suite..."
        pid = spawn(env, "bundle exec rake",
                   out: $stdout,
                   err: $stderr,
                   pgroup: true)

        begin
          _, status = Process.wait2(pid)
          status.exitstatus
        rescue Interrupt
          puts "\nInterrupted! Terminating test process..."
          begin
            Process.kill("TERM", -pid) # Kill the process group
            sleep(2)
            Process.kill("KILL", -pid) if process_running?(pid)
          rescue Errno::ESRCH
            # Process already terminated
          end
          130 # Standard exit code for SIGINT
        end
      end

      def self.bundle_update_needed?(env)
        # Check if Gemfile.lock exists and is up to date
        gemfile_path = env["BUNDLE_GEMFILE"] || "Gemfile"
        lockfile_path = "#{gemfile_path}.lock"

        return true unless File.exist?(lockfile_path)

        # Check if Gemfile is newer than Gemfile.lock
        return true if File.mtime(gemfile_path) > File.mtime(lockfile_path)

        # For Rails version changes, we need to check if the current lockfile
        # is compatible with the requested Rails version
        if env["RAILS_VERSION"] && lockfile_has_incompatible_rails_version?(lockfile_path, env["RAILS_VERSION"])
          return true
        end

        # Check if bundle check passes with the current environment
        system(env, "bundle check > /dev/null 2>&1") == false
      end

      def self.lockfile_has_incompatible_rails_version?(lockfile_path, target_rails_version)
        return false unless File.exist?(lockfile_path)

        lockfile_content = File.read(lockfile_path)

        # Extract Rails version from lockfile
        if lockfile_content =~ /^\s*rails \(([^)]+)\)/
          locked_rails_version = $1
          target_major_minor = target_rails_version.split('.')[0..1].join('.')
          locked_major_minor = locked_rails_version.split('.')[0..1].join('.')

          # If major.minor versions don't match, we need to update
          return target_major_minor != locked_major_minor
        end

        # If we can't determine the Rails version, assume update is needed
        true
      end

      def self.update_bundle(env)
        puts "Running bundle install..."
        # For Rails version changes, we need to update the bundle to get the right dependencies
        if env["RAILS_VERSION"]
          puts "Updating dependencies for Rails #{env['RAILS_VERSION']}..."
          # Force recompilation of native gems to ensure compatibility with current Ruby version
          success = system(env, "bundle update --force") &&
                   system(env, "bundle pristine sqlite3 > /dev/null 2>&1") &&
                   system(env, "bundle check > /dev/null 2>&1")

          unless success
            puts "Bundle update failed, trying alternative approach..."
            # Alternative: remove problematic gems and reinstall
            system(env, "gem uninstall sqlite3 --force > /dev/null 2>&1")
            success = system(env, "bundle install --force") &&
                     system(env, "bundle check > /dev/null 2>&1")
          end

          success
        else
          system(env, "bundle install") &&
          system(env, "bundle check > /dev/null 2>&1")
        end
      end

      def self.process_running?(pid)
        Process.getpgid(pid)
        true
      rescue Errno::ESRCH
        false
      end
    end
  end
end
