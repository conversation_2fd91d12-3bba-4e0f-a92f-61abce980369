# frozen_string_literal: true

require "rake/clean"
require "rspec/core/rake_task"

module Sentry
  module Test
    module RakeTasks
      extend Rake::DSL

      def self.define_spec_tasks(options = {})
        opts = {
          isolated_specs_pattern: "spec/isolated/**/*_spec.rb",
          spec_pattern: nil,
          spec_exclude_pattern: nil,
          spec_rspec_opts: nil,
          isolated_rspec_opts: nil
        }.merge(options)

        RSpec::Core::RakeTask.new(:spec).tap do |task|
          task.pattern = opts[:spec_pattern] if opts[:spec_pattern]
          task.exclude_pattern = opts[:spec_exclude_pattern] if opts[:spec_exclude_pattern]
          task.rspec_opts = opts[:spec_rspec_opts] if opts[:spec_rspec_opts]
        end

        namespace :spec do
          RSpec::Core::RakeTask.new(:isolated).tap do |task|
            task.pattern = opts[:isolated_specs_pattern]
            task.rspec_opts = opts[:isolated_rspec_opts] if opts[:isolated_rspec_opts]
          end
        end
      end

      # Define versioned specs task (sentry-rails specific)
      def self.define_versioned_specs_task(options = {})
        opts = {
          rspec_opts: "--order rand"
        }.merge(options)

        namespace :spec do
          RSpec::Core::RakeTask.new(:versioned).tap do |task|
            ruby_ver_dir = RUBY_VERSION.split(".")[0..1].join(".")
            matching_dir = Dir["spec/versioned/*"].detect { |dir| File.basename(dir) <= ruby_ver_dir }

            unless matching_dir
              puts "No versioned specs found for ruby #{RUBY_VERSION}"
              exit 0
            end

            puts "Running versioned specs from #{matching_dir} for ruby #{RUBY_VERSION}"

            task.rspec_opts = opts[:rspec_opts]
            task.pattern = "#{matching_dir}/**/*_spec.rb"
          end
        end
      end

      # Define Rails version-specific tasks (sentry-rails specific)
      def self.define_rails_version_tasks(options = {})
        # Supported Rails versions based on CI configuration
        supported_rails_versions = %w[5.0 5.1 5.2 6.0 6.1 7.0 7.1 8.0]

        namespace :spec do
          namespace :version do
            # Define individual version tasks for convenience
            supported_rails_versions.each do |version|
              desc "Run specs with Rails #{version}"
              task version do
                puts "Running specs with Rails #{version}..."
                env = { "RAILS_VERSION" => version }
                system(env, "bundle exec rake") || abort("Specs failed for Rails #{version}")
              end
            end

            # Define task to run all supported Rails versions
            desc "Run specs for all supported Rails versions"
            task :all do
              puts "Running specs for all supported Rails versions: #{supported_rails_versions.join(', ')}"

              failed_versions = []

              supported_rails_versions.each do |version|
                puts "\n" + "="*60
                puts "Testing Rails #{version}"
                puts "="*60

                env = { "RAILS_VERSION" => version }
                success = system(env, "bundle exec rake")

                if success
                  puts "✓ Rails #{version} - PASSED"
                else
                  puts "✗ Rails #{version} - FAILED"
                  failed_versions << version
                end
              end

              puts "\n" + "="*60
              puts "SUMMARY"
              puts "="*60

              if failed_versions.empty?
                puts "✓ All Rails versions passed!"
              else
                puts "✗ Failed versions: #{failed_versions.join(', ')}"
                abort("Some Rails versions failed. See output above for details.")
              end
            end
          end

          # Define parameterized task that accepts version as argument
          desc "Run specs with specific Rails version (e.g., rake spec:version[6.1])"
          task :version, [:rails_version] do |t, args|
            version = args[:rails_version]
            unless version
              puts "Error: Rails version is required. Usage: rake spec:version[6.1]"
              abort
            end

            unless supported_rails_versions.include?(version)
              puts "Error: Unsupported Rails version '#{version}'"
              puts "Supported versions: #{supported_rails_versions.join(', ')}"
              abort
            end

            puts "Running specs with Rails #{version}..."
            env = { "RAILS_VERSION" => version }
            system(env, "bundle exec rake") || abort("Specs failed for Rails #{version}")
          end
        end
      end
    end
  end
end
